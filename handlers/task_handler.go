package handlers

import (
	"net/http"
	"strconv"

	"golden_crawler/models"
	"golden_crawler/services"

	"github.com/gin-gonic/gin"
)

// TaskHandler 任务处理器
type TaskHandler struct {
	taskService services.TaskService
}

// NewTaskHandler 创建新的任务处理器
func NewTaskHandler(taskService services.TaskService) *TaskHandler {
	return &TaskHandler{
		taskService: taskService,
	}
}

// CreateTask 创建新的爬虫任务
// @Summary 创建爬虫任务
// @Description 创建一个新的爬虫任务
// @Tags tasks
// @Accept json
// @Produce json
// @Param task body models.CreateTaskRequest true "任务信息"
// @Success 201 {object} models.CreateTaskResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks [post]
func (h *TaskHandler) CreateTask(c *gin.Context) {
	var req models.CreateTaskRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSO<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 验证初始URL列表
	for i, url := range req.InitialURLs {
		if url.Method == models.POST && url.Body == "" {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "POST请求必须包含请求体",
				Error:   "initial_urls[" + strconv.Itoa(i) + "].body is required for POST method",
			})
			return
		}
	}

	// 调用服务层创建任务
	task, err := h.taskService.CreateTask(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "创建任务失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusCreated, models.CreateTaskResponse{
		Success: true,
		Message: "任务创建成功",
		Task:    task,
	})
}

// GetTask 获取任务信息
// @Summary 获取任务信息
// @Description 根据任务ID获取任务详细信息
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.CrawlerTask
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id} [get]
func (h *TaskHandler) GetTask(c *gin.Context) {
	taskID := c.Param("id")
	
	task, err := h.taskService.GetTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "任务不存在",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, task)
}

// ListTasks 获取任务列表
// @Summary 获取任务列表
// @Description 获取爬虫任务列表，支持分页
// @Tags tasks
// @Produce json
// @Param limit query int false "每页数量" default(10)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {array} models.CrawlerTask
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks [get]
func (h *TaskHandler) ListTasks(c *gin.Context) {
	limit := 10
	offset := 0

	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 {
			limit = parsed
		}
	}

	if o := c.Query("offset"); o != "" {
		if parsed, err := strconv.Atoi(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}

	tasks, err := h.taskService.ListTasks(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取任务列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tasks,
		"total":   len(tasks),
	})
}

// UpdateTaskStatus 更新任务状态
// @Summary 更新任务状态
// @Description 更新指定任务的状态
// @Tags tasks
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Param status body object true "状态信息"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id}/status [put]
func (h *TaskHandler) UpdateTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	
	var req struct {
		Status models.TaskStatus `json:"status" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	err := h.taskService.UpdateTaskStatus(c.Request.Context(), taskID, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "更新任务状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务状态更新成功",
	})
}

// DeleteTask 删除任务
// @Summary 删除任务
// @Description 删除指定的爬虫任务
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id} [delete]
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	taskID := c.Param("id")
	
	err := h.taskService.DeleteTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "删除任务失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务删除成功",
	})
}
