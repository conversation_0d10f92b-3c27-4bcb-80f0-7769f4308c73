package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"golden_crawler/config"
	"golden_crawler/handlers"
	"golden_crawler/services"

	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()
	
	// 创建Dapr服务
	daprService := daprd.NewService(":" + cfg.Dapr.AppPort)
	
	// 初始化服务层
	taskService := services.NewTaskService()
	
	// 初始化处理器
	taskHandler := handlers.NewTaskHandler(taskService)
	
	// 设置路由
	setupRoutes(daprService, taskHandler)
	
	// 启动服务
	go func() {
		fmt.Printf("启动爬虫任务管理服务，端口: %s\n", cfg.Dapr.AppPort)
		if err := daprService.Start(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动Dapr服务失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	fmt.Println("正在关闭服务器...")
	
	// 创建一个5秒的超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// 优雅关闭服务器
	if err := daprService.GracefulStop(ctx); err != nil {
		log.Printf("服务器强制关闭: %v", err)
	}
	
	fmt.Println("服务器已关闭")
}

// setupRoutes 设置路由
func setupRoutes(s common.Service, taskHandler *handlers.TaskHandler) {
	// 健康检查端点
	s.AddHealthCheckHandler("/health", func(ctx context.Context) error {
		return nil
	})
	
	// 创建Gin路由器
	router := gin.Default()
	
	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	
	// API版本1路由组
	v1 := router.Group("/api/v1")
	{
		// 任务相关路由
		tasks := v1.Group("/tasks")
		{
			tasks.POST("", taskHandler.CreateTask)           // 创建任务
			tasks.GET("", taskHandler.ListTasks)             // 获取任务列表
			tasks.GET("/:id", taskHandler.GetTask)           // 获取单个任务
			tasks.PUT("/:id/status", taskHandler.UpdateTaskStatus) // 更新任务状态
			tasks.DELETE("/:id", taskHandler.DeleteTask)     // 删除任务
		}
	}
	
	// 根路径信息
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "爬虫任务管理服务",
			"version": "1.0.0",
			"status":  "running",
		})
	})
	
	// 将Gin路由器添加到Dapr服务
	s.AddServiceInvocationHandler("/", router.ServeHTTP)
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
