# 爬虫任务管理服务

基于Go语言和Dapr框架实现的爬虫任务管理服务。

## 功能特性

- 创建爬虫任务
- 任务状态管理
- 任务列表查询
- 支持GET和POST请求的初始URL配置
- 任务优先级管理
- 下载器和解析器配置

## 项目结构

```
golden_crawler/
├── main.go                    # 服务入口点
├── go.mod                     # Go模块定义
├── config/
│   └── config.go             # 应用配置
├── models/
│   └── task.go               # 数据模型定义
├── services/
│   └── task_service.go       # 业务逻辑服务
├── handlers/
│   └── task_handler.go       # HTTP处理器
└── dapr/
    ├── config.yaml           # Dapr配置
    └── components/
        ├── statestore.yaml   # 状态存储组件
        ├── pubsub.yaml       # 发布订阅组件
        └── secretstore.yaml  # 密钥存储组件
```

## API接口

### 创建任务
```
POST /api/v1/tasks
```

请求体示例：
```json
{
  "name": "示例爬虫任务",
  "description": "这是一个示例爬虫任务",
  "initial_urls": [
    {
      "url": "https://example.com",
      "method": "GET"
    },
    {
      "url": "https://api.example.com/data",
      "method": "POST",
      "body": "{\"key\": \"value\"}"
    }
  ],
  "priority": 2,
  "downloader_name": "default_downloader",
  "parser_name": "default_parser"
}
```

### 获取任务列表
```
GET /api/v1/tasks?limit=10&offset=0
```

### 获取单个任务
```
GET /api/v1/tasks/{id}
```

### 更新任务状态
```
PUT /api/v1/tasks/{id}/status
```

请求体：
```json
{
  "status": "running"
}
```

### 删除任务
```
DELETE /api/v1/tasks/{id}
```

## 运行服务

### 前置条件
- Go 1.21+
- Dapr CLI
- Redis (用于状态存储和发布订阅)

### 安装依赖
```bash
go mod tidy
```

### 启动Redis
```bash
docker run -d --name redis -p 6379:6379 redis:latest
```

### 启动服务
```bash
dapr run --app-id crawler-task-manager --app-port 8080 --dapr-http-port 3500 --components-path ./dapr/components --config ./dapr/config.yaml -- go run main.go
```

### 测试API
```bash
# 创建任务
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试任务",
    "description": "这是一个测试任务",
    "initial_urls": [
      {
        "url": "https://httpbin.org/get",
        "method": "GET"
      }
    ],
    "priority": 2,
    "downloader_name": "default_downloader",
    "parser_name": "default_parser"
  }'

# 获取任务列表
curl http://localhost:8080/api/v1/tasks
```

## 环境变量

- `SERVER_PORT`: 服务器端口 (默认: 8080)
- `SERVER_HOST`: 服务器主机 (默认: 0.0.0.0)
- `DAPR_APP_ID`: Dapr应用ID (默认: crawler-task-manager)
- `DAPR_APP_PORT`: Dapr应用端口 (默认: 8080)
- `DAPR_STATE_STORE`: 状态存储名称 (默认: statestore)
- `DAPR_PUBSUB_NAME`: 发布订阅名称 (默认: pubsub)
- `DAPR_SECRET_STORE`: 密钥存储名称 (默认: secretstore)

## 开发说明

当前版本的服务接口已经完成，但内部实现留空，需要根据具体需求补充：

1. **数据持久化**: 在 `services/task_service.go` 中实现与数据库或Dapr状态存储的交互
2. **事件发布**: 实现任务创建、状态变更等事件的发布
3. **任务队列**: 实现任务调度和队列管理
4. **错误处理**: 完善错误处理和日志记录
5. **认证授权**: 添加API认证和授权机制
6. **监控指标**: 添加服务监控和指标收集
