# Makefile for Crawler Task Manager Service

.PHONY: build run test clean deps dapr-run dapr-stop docker-build docker-run

# 变量定义
APP_NAME=crawler-task-manager
APP_PORT=8080
DAPR_HTTP_PORT=3500
DAPR_GRPC_PORT=50001

# 构建应用
build:
	go build -o bin/$(APP_NAME) main.go

# 安装依赖
deps:
	go mod tidy
	go mod download

# 运行测试
test:
	go test -v ./...

# 清理构建文件
clean:
	rm -rf bin/
	go clean

# 直接运行Go应用（不使用Dapr）
run:
	go run main.go

# 使用Dapr运行应用
dapr-run:
	dapr run \
		--app-id $(APP_NAME) \
		--app-port $(APP_PORT) \
		--dapr-http-port $(DAPR_HTTP_PORT) \
		--dapr-grpc-port $(DAPR_GRPC_PORT) \
		--components-path ./dapr/components \
		--config ./dapr/config.yaml \
		-- go run main.go

# 停止Dapr应用
dapr-stop:
	dapr stop $(APP_NAME)

# 启动Redis（用于开发）
redis-start:
	docker run -d --name redis-crawler -p 6379:6379 redis:latest

# 停止Redis
redis-stop:
	docker stop redis-crawler
	docker rm redis-crawler

# 构建Docker镜像
docker-build:
	docker build -t $(APP_NAME):latest .

# 运行Docker容器
docker-run:
	docker run -p $(APP_PORT):$(APP_PORT) $(APP_NAME):latest

# 格式化代码
fmt:
	go fmt ./...

# 代码检查
lint:
	golangci-lint run

# 生成API文档
docs:
	swag init

# 开发环境设置
dev-setup: deps redis-start
	@echo "开发环境设置完成"

# 测试API
test-api:
	@echo "测试创建任务API..."
	curl -X POST http://localhost:$(APP_PORT)/api/v1/tasks \
		-H "Content-Type: application/json" \
		-d '{ \
			"name": "测试任务", \
			"description": "这是一个测试任务", \
			"initial_urls": [{ \
				"url": "https://httpbin.org/get", \
				"method": "GET" \
			}], \
			"priority": 2, \
			"downloader_name": "default_downloader", \
			"parser_name": "default_parser" \
		}'
	@echo "\n\n测试获取任务列表API..."
	curl http://localhost:$(APP_PORT)/api/v1/tasks

# 帮助信息
help:
	@echo "可用的命令:"
	@echo "  build        - 构建应用"
	@echo "  deps         - 安装依赖"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理构建文件"
	@echo "  run          - 直接运行应用"
	@echo "  dapr-run     - 使用Dapr运行应用"
	@echo "  dapr-stop    - 停止Dapr应用"
	@echo "  redis-start  - 启动Redis容器"
	@echo "  redis-stop   - 停止Redis容器"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  fmt          - 格式化代码"
	@echo "  lint         - 代码检查"
	@echo "  dev-setup    - 设置开发环境"
	@echo "  test-api     - 测试API接口"
	@echo "  help         - 显示帮助信息"
