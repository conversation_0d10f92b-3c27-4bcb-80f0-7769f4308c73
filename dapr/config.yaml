apiVersion: dapr.io/v1alpha1
kind: Configuration
metadata:
  name: crawler-config
spec:
  tracing:
    samplingRate: "1"
    zipkin:
      endpointAddress: "http://localhost:9411/api/v2/spans"
  metric:
    enabled: true
  secrets:
    scopes:
      - storeName: "secretstore"
        defaultAccess: "allow"
  accessControl:
    defaultAction: "allow"
    trustDomain: "public"
    policies:
      - appId: "crawler-task-manager"
        defaultAction: "allow"
        trustDomain: "public"
        namespace: "default"
  nameResolution:
    component: "mdns"
    version: "v1"
    configuration:
      selfRegister: true
