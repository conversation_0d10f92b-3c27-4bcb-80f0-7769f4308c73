package models

import (
	"time"
)

// HTTPMethod 定义HTTP请求方法
type HTTPMethod string

const (
	GET  HTTPMethod = "GET"
	POST HTTPMethod = "POST"
)

// InitialURL 定义初始化URL结构
type InitialURL struct {
	URL    string     `json:"url" binding:"required"`
	Method HTTPMethod `json:"method" binding:"required"`
	Body   string     `json:"body,omitempty"` // 仅当Method为POST时使用
}

// TaskPriority 定义任务优先级
type TaskPriority int

const (
	LowPriority    TaskPriority = 1
	MediumPriority TaskPriority = 2
	HighPriority   TaskPriority = 3
)

// TaskStatus 定义任务状态
type TaskStatus string

const (
	StatusPending   TaskStatus = "pending"
	StatusRunning   TaskStatus = "running"
	StatusCompleted TaskStatus = "completed"
	StatusFailed    TaskStatus = "failed"
	StatusPaused    TaskStatus = "paused"
)

// CrawlerTask 定义爬虫任务结构
type CrawlerTask struct {
	ID           string       `json:"id"`
	Name         string       `json:"name" binding:"required"`
	Description  string       `json:"description"`
	InitialURLs  []InitialURL `json:"initial_urls" binding:"required,min=1"`
	Priority     TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	DownloaderName string     `json:"downloader_name" binding:"required"`
	ParserName   string       `json:"parser_name" binding:"required"`
	Status       TaskStatus   `json:"status"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
}

// CreateTaskRequest 定义创建任务的请求结构
type CreateTaskRequest struct {
	Name           string       `json:"name" binding:"required"`
	Description    string       `json:"description"`
	InitialURLs    []InitialURL `json:"initial_urls" binding:"required,min=1"`
	Priority       TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	DownloaderName string       `json:"downloader_name" binding:"required"`
	ParserName     string       `json:"parser_name" binding:"required"`
}

// CreateTaskResponse 定义创建任务的响应结构
type CreateTaskResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Task    *CrawlerTask `json:"task,omitempty"`
}

// ErrorResponse 定义错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}
